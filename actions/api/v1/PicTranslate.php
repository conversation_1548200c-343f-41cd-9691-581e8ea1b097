<?php
/**
 * @file PicTranslate.php
 * @brief 图片翻译接口
 * @version 1.0
 * @date 2025-07-28
 */

class Action_Pictranslate extends Kdproduct_Action_MisAuthBase
{
    public function invoke()
    {
        header("Content-Type: application/json; charset=utf-8");

        $logPre = "Action_Pictranslate";
        Bd_Log::notice("{$logPre} invoke start");

        try {
            // 获取JSON数据
            $rawInput = file_get_contents('php://input');
            Bd_Log::notice("{$logPre} raw input: " . $rawInput);

            $input = json_decode($rawInput, true);

            if (!$input) {
                Bd_Log::warning("{$logPre} invalid JSON data");
                throw new Exception('无效的JSON数据');
            }

            Bd_Log::notice("{$logPre} parsed input: " . json_encode($input));

            // 验证必需参数
            if (empty($input['appId'])) {
                Bd_Log::warning("{$logPre} appId is empty");
                throw new Exception('appId不能为空');
            }

            if (empty($input['imageUrl'])) {
                Bd_Log::warning("{$logPre} imageUrl is empty");
                throw new Exception('imageUrl不能为空');
            }

            Bd_Log::notice("{$logPre} calling Service_Data_Kdtools_Translate::PicTranslate");

            // 调用翻译服务
            $result = Service_Data_Kdtools_Translate::PicTranslate(
                $input['appId'],
                $input['transFrom'] ?? 'auto',
                $input['transTo'] ?? 'zh',
                $input['imageUrl']
            );

            if ($result === false) {
                Bd_Log::warning("{$logPre} PicTranslate returned false");
                throw new Exception('翻译失败');
            }

            Bd_Log::notice("{$logPre} PicTranslate success, result: " . json_encode($result));

            // 返回结果
            echo json_encode([
                'success' => true,
                'data' => $result
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            Bd_Log::warning("{$logPre} exception: " . $e->getMessage());
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
}
