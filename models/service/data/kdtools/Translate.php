<?php
/**
 * @befie 	kdtools拍照翻译服务
 * @file 	Translate.php
 * <AUTHOR> @version 1.0
 * @since   1.0
 * @date 	2025/7/28
 */
class Service_Data_Kdtools_Translate
{
    /**
     * 图片翻译接口
     * @param string $appId 应用ID
     * @param string $transFrom 源语言
     * @param string $transTo 目标语言
     * @param string $imageUrl 图片URL
     * @return array|false
     */
    public static function PicTranslate($appId, $transFrom, $transTo, $imageUrl)
    {
        $logPre = "PicTranslate";
        Bd_Log::notice("{$logPre} start with params: appId={$appId}, transFrom={$transFrom}, transTo={$transTo}, imageUrl={$imageUrl}");

        if (empty($appId) || empty($transFrom) || empty($transTo) || empty($imageUrl)) {
            Bd_Log::warning("{$logPre} required params is empty: appId=" . ($appId ?: 'empty') . ", transFrom=" . ($transFrom ?: 'empty') . ", transTo=" . ($transTo ?: 'empty') . ", imageUrl=" . ($imageUrl ?: 'empty'));
            return false;
        }

        Bd_Log::notice("{$logPre} params validation passed, starting to download image");

        // 下载图片到内存
        $imageContent = self::downloadImage($imageUrl);
        if ($imageContent === false) {
            Bd_Log::warning("{$logPre} failed to download image from: " . $imageUrl);
            return false;
        }

        Bd_Log::notice("{$logPre} image downloaded successfully, size: " . strlen($imageContent) . " bytes");

        // 准备请求参数
        $params = [
            'appId' => $appId,
            'transFrom' => $transFrom,
            'transTo' => $transTo
        ];

        Bd_Log::notice("{$logPre} calling postMultipart with params: " . json_encode($params));

        $result = self::postMultipart($params, $imageContent, $imageUrl);

        if ($result === false) {
            Bd_Log::warning("{$logPre} postMultipart returned false");
            return false;
        }

        Bd_Log::notice("{$logPre} success, result: " . json_encode($result));
        return $result;
    }

    /**
     * 下载图片到内存
     * @param string $imageUrl 图片URL
     * @return string|false 图片内容或失败时返回false
     */
    private static function downloadImage($imageUrl)
    {
        $logPre = "downloadImage";
        Bd_Log::notice("{$logPre} start downloading from: {$imageUrl}");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $imageUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);

        Bd_Log::notice("{$logPre} curl options set, executing request");

        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);

        Bd_Log::notice("{$logPre} curl completed: httpCode={$httpCode}, contentType={$contentType}, error=" . ($error ?: 'none') . ", contentSize=" . (is_string($imageContent) ? strlen($imageContent) : 'false'));

        if ($imageContent === false || !empty($error) || $httpCode !== 200) {
            $logContext = [
                'imageUrl' => $imageUrl,
                'httpCode' => $httpCode,
                'error' => $error,
                'contentType' => $contentType
            ];
            Bd_Log::warning("{$logPre} failed: " . json_encode($logContext));
            return false;
        }

        Bd_Log::notice("{$logPre} success: downloaded " . strlen($imageContent) . " bytes");
        return $imageContent;
    }



    /**
     * 发送multipart/form-data POST请求（纯内存版本）
     * @param array $params 表单参数
     * @param string $imageContent 图片内容
     * @param string $imageUrl 原始图片URL（用于获取文件名）
     * @return array|false
     */
    private static function postMultipart($params, $imageContent, $imageUrl)
    {
        $logPre = "postMultipart";
        Bd_Log::notice("{$logPre} start with params: " . json_encode($params) . ", imageSize: " . strlen($imageContent) . ", imageUrl: {$imageUrl}");

        // 获取文件名
        $fileName = basename(parse_url($imageUrl, PHP_URL_PATH));
        if (empty($fileName) || strpos($fileName, '.') === false) {
            $fileName = 'image.jpg'; // 默认文件名
        }
        Bd_Log::notice("{$logPre} using fileName: {$fileName}");

        // 使用RAL调用kdtools服务
        try {
            // 准备multipart数据
            $boundary = '----formdata-' . md5(uniqid() . microtime());
            $postData = self::buildMultipartBody($params, $imageContent, $fileName, $boundary);
            Bd_Log::notice("{$logPre} multipart body built, size: " . strlen($postData) . ", boundary: {$boundary}");

            // 使用RAL调用
            $ral = new Ext_Ral();

            Bd_Log::notice("{$logPre} calling RAL with service: kdtools, path: /kdtools/service/translate/pictranslate");

            // 通过RAL调用kdtools服务
            $response = $ral->ral('kdtools', 'POST', $postData, '/kdtools/service/translate/pictranslate', [
                'Content-Type: multipart/form-data; boundary=' . $boundary,
                'Content-Length: ' . strlen($postData)
            ]);

            if ($response === false) {
                Bd_Log::warning("{$logPre} RAL call failed, errno: " . $ral->ralErrno . ", error: " . $ral->ralError);
                return false;
            }

            Bd_Log::notice("{$logPre} RAL call success, response size: " . strlen($response));

            // 解析响应
            $responseData = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Bd_Log::warning("{$logPre} json decode error: " . json_last_error_msg() . ", response: " . substr($response, 0, 500));
                return false;
            }

            if (!isset($responseData['errNo']) || !isset($responseData['data']) || $responseData['errNo'] != 0) {
                $logContext = [
                    'service' => 'kdtools',
                    'path' => '/kdtools/service/translate/pictranslate',
                    'result' => $responseData
                ];
                Bd_Log::warning("{$logPre} business error: " . json_encode($logContext));
                return false;
            }

            Bd_Log::notice("{$logPre} success, returning data");
            return $responseData['data'];

        } catch (Exception $e) {
            Bd_Log::warning("{$logPre} RAL exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 构建multipart/form-data请求体（纯内存操作）
     * @param array $params 表单参数
     * @param string $imageContent 图片内容
     * @param string $fileName 文件名
     * @param string $boundary 边界字符串
     * @return string
     */
    private static function buildMultipartBody($params, $imageContent, $fileName, $boundary)
    {
        $data = '';

        // 添加普通表单字段
        foreach ($params as $key => $value) {
            $data .= "--{$boundary}\r\n";
            $data .= "Content-Disposition: form-data; name=\"{$key}\"\r\n\r\n";
            $data .= "{$value}\r\n";
        }

        // 添加文件字段
        $data .= "--{$boundary}\r\n";
        $data .= "Content-Disposition: form-data; name=\"image\"; filename=\"{$fileName}\"\r\n";
        $data .= "Content-Type: image/jpeg\r\n\r\n";
        $data .= $imageContent . "\r\n";
        $data .= "--{$boundary}--\r\n";

        return $data;
    }



}
